import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.font_manager import FontProperties

def rolling_period_analysis():
    # 讀取CSV檔案
    df = pd.read_csv('data/lotto_history_2007_2025.csv')
    df.columns = ['filename', 'game_type', 'period', 'date', 'num1', 'num2', 'num3', 'num4', 'num5', 'num6']
    
    print('=== 滾動期數連續性分析 ===')
    print(f'總期數: {len(df)}')
    print()
    
    # 轉換期號為整數
    df['period'] = df['period'].astype(int)
    df['year'] = df['date'].str.split('/').str[0].astype(int)
    
    # 計算期號差異
    df['period_diff'] = df['period'].diff()
    
    # 定義不同的滾動窗口大小
    window_sizes = [10, 20, 50, 100]
    
    print('=== 不同窗口大小的滾動分析 ===')
    
    for window_size in window_sizes:
        print(f'\n--- {window_size}期滾動窗口分析 ---')
        
        # 計算滾動統計
        rolling_stats = []
        
        for i in range(window_size - 1, len(df)):
            window_data = df.iloc[i-window_size+1:i+1]
            
            # 計算窗口內的期號差異
            diffs = window_data['period_diff'].dropna()
            
            # 統計連續性指標
            continuous_count = len(diffs[diffs == 1])  # 連續的期數
            gap_count = len(diffs[diffs > 1])  # 有跳躍的期數
            total_gaps = diffs[diffs > 1].sum() - gap_count  # 總跳躍大小
            
            # 計算連續性比例
            continuity_ratio = continuous_count / len(diffs) if len(diffs) > 0 else 0
            
            rolling_stats.append({
                '位置': i + 1,
                '開始期號': window_data.iloc[0]['period'],
                '結束期號': window_data.iloc[-1]['period'],
                '開始日期': window_data.iloc[0]['date'],
                '結束日期': window_data.iloc[-1]['date'],
                '連續期數': continuous_count,
                '跳躍期數': gap_count,
                '總跳躍大小': total_gaps,
                '連續性比例': continuity_ratio,
                '平均跳躍大小': total_gaps / gap_count if gap_count > 0 else 0
            })
        
        rolling_df = pd.DataFrame(rolling_stats)
        
        # 顯示統計摘要
        print(f'窗口大小: {window_size}期')
        print(f'滾動樣本數: {len(rolling_df)}')
        print(f'平均連續性比例: {rolling_df["連續性比例"].mean():.3f}')
        print(f'最小連續性比例: {rolling_df["連續性比例"].min():.3f}')
        print(f'最大連續性比例: {rolling_df["連續性比例"].max():.3f}')
        print(f'總跳躍次數: {rolling_df["跳躍期數"].sum()}')
        print(f'平均跳躍大小: {rolling_df["平均跳躍大小"].mean():.1f}')
        
        # 找出連續性最差的窗口
        worst_windows = rolling_df.nsmallest(5, '連續性比例')
        print(f'\n連續性最差的5個{window_size}期窗口:')
        for _, row in worst_windows.iterrows():
            print(f'  位置{row["位置"]}: {row["開始期號"]}-{row["結束期號"]} '
                  f'({row["開始日期"]}-{row["結束日期"]}) '
                  f'連續性: {row["連續性比例"]:.3f}')
    
    # 動態窗口分析
    print('\n=== 動態窗口分析 ===')
    
    # 分析不同時間段的連續性
    time_periods = [
        ('2015-2017', 2015, 2017),
        ('2018-2020', 2018, 2020),
        ('2021-2023', 2021, 2023),
        ('2024-2025', 2024, 2025)
    ]
    
    for period_name, start_year, end_year in time_periods:
        period_data = df[(df['year'] >= start_year) & (df['year'] <= end_year)]
        
        if len(period_data) == 0:
            continue
            
        diffs = period_data['period_diff'].dropna()
        continuous_count = len(diffs[diffs == 1])
        gap_count = len(diffs[diffs > 1])
        total_gaps = diffs[diffs > 1].sum() - gap_count if gap_count > 0 else 0
        
        print(f'\n{period_name} ({len(period_data)}期):')
        print(f'  連續期數: {continuous_count}')
        print(f'  跳躍期數: {gap_count}')
        print(f'  連續性比例: {continuous_count/len(diffs):.3f}')
        print(f'  平均跳躍大小: {total_gaps/gap_count:.1f}' if gap_count > 0 else '  平均跳躍大小: 0')
    
    # 滾動連續性趨勢分析
    print('\n=== 滾動連續性趨勢分析 ===')
    
    # 使用50期窗口進行趨勢分析
    window_size = 50
    trend_data = []
    
    for i in range(window_size - 1, len(df)):
        window_data = df.iloc[i-window_size+1:i+1]
        diffs = window_data['period_diff'].dropna()
        continuous_count = len(diffs[diffs == 1])
        continuity_ratio = continuous_count / len(diffs) if len(diffs) > 0 else 0
        
        trend_data.append({
            '位置': i + 1,
            '期號': window_data.iloc[-1]['period'],
            '日期': window_data.iloc[-1]['date'],
            '連續性比例': continuity_ratio
        })
    
    trend_df = pd.DataFrame(trend_data)
    
    # 計算趨勢線
    x = np.arange(len(trend_df))
    y = trend_df['連續性比例'].values
    z = np.polyfit(x, y, 1)
    trend_line = np.poly1d(z)
    
    print(f'趨勢線斜率: {z[0]:.6f}')
    print(f'趨勢線截距: {z[1]:.3f}')
    
    if z[0] > 0.001:
        print('趨勢: 連續性逐漸改善')
    elif z[0] < -0.001:
        print('趨勢: 連續性逐漸惡化')
    else:
        print('趨勢: 連續性保持穩定')
    
    # 找出連續性變化最大的時期
    trend_df['連續性變化'] = trend_df['連續性比例'].diff()
    
    print(f'\n連續性改善最大的時期:')
    improvement_periods = trend_df.nlargest(3, '連續性變化')
    for _, row in improvement_periods.iterrows():
        print(f'  位置{row["位置"]}: 期號{row["期號"]} ({row["日期"]}) 改善{row["連續性變化"]:.3f}')
    
    print(f'\n連續性惡化最大的時期:')
    deterioration_periods = trend_df.nsmallest(3, '連續性變化')
    for _, row in deterioration_periods.iterrows():
        print(f'  位置{row["位置"]}: 期號{row["期號"]} ({row["日期"]}) 惡化{abs(row["連續性變化"]):.3f}')
    
    # 年度滾動分析
    print('\n=== 年度滾動分析 ===')
    
    yearly_rolling = []
    
    for year in sorted(df['year'].unique()):
        year_data = df[df['year'] == year]
        if len(year_data) < 2:
            continue
            
        diffs = year_data['period_diff'].dropna()
        continuous_count = len(diffs[diffs == 1])
        gap_count = len(diffs[diffs > 1])
        total_gaps = diffs[diffs > 1].sum() - gap_count if gap_count > 0 else 0
        
        yearly_rolling.append({
            '年份': year,
            '期數': len(year_data),
            '連續期數': continuous_count,
            '跳躍期數': gap_count,
            '連續性比例': continuous_count / len(diffs),
            '平均跳躍大小': total_gaps / gap_count if gap_count > 0 else 0
        })
    
    yearly_df = pd.DataFrame(yearly_rolling)
    
    print('年度連續性統計:')
    for _, row in yearly_df.iterrows():
        print(f'  {row["年份"]}年: {row["期數"]}期, 連續性{row["連續性比例"]:.3f}, '
              f'跳躍{row["跳躍期數"]}次, 平均跳躍{row["平均跳躍大小"]:.1f}')
    
    # 找出最佳和最差年度
    best_year = yearly_df.loc[yearly_df['連續性比例'].idxmax()]
    worst_year = yearly_df.loc[yearly_df['連續性比例'].idxmin()]
    
    print(f'\n連續性最佳年度: {best_year["年份"]}年 (連續性{best_year["連續性比例"]:.3f})')
    print(f'連續性最差年度: {worst_year["年份"]}年 (連續性{worst_year["連續性比例"]:.3f})')
    
    return df, trend_df, yearly_df

def visualize_rolling_analysis(df, trend_df, yearly_df):
    """視覺化滾動分析結果"""
    try:
        font = FontProperties(fname=r"c:\windows\fonts\msjh.ttc", size=12)
    except FileNotFoundError:
        try:
            font = FontProperties(fname="/System/Library/Fonts/PingFang.ttc", size=12)
        except FileNotFoundError:
            font = FontProperties(size=12)
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('滾動期數連續性分析', fontsize=16, fontproperties=font)
    
    # 1. 期號差異分布
    diffs = df['period_diff'].dropna()
    ax1.hist(diffs, bins=range(1, int(diffs.max())+2), alpha=0.7, edgecolor='black')
    ax1.set_title('期號差異分布', fontproperties=font)
    ax1.set_xlabel('期號差異', fontproperties=font)
    ax1.set_ylabel('頻率', fontproperties=font)
    ax1.grid(True, alpha=0.3)
    
    # 2. 滾動連續性趨勢
    x = np.arange(len(trend_df))
    y = trend_df['連續性比例'].values
    ax2.plot(x, y, 'b-', alpha=0.7, label='實際連續性')
    
    # 添加趨勢線
    z = np.polyfit(x, y, 1)
    trend_line = np.poly1d(z)
    ax2.plot(x, trend_line(x), 'r--', alpha=0.8, label=f'趨勢線 (斜率: {z[0]:.4f})')
    
    ax2.set_title('50期滾動連續性趨勢', fontproperties=font)
    ax2.set_xlabel('滾動位置', fontproperties=font)
    ax2.set_ylabel('連續性比例', fontproperties=font)
    ax2.legend(prop=font)
    ax2.grid(True, alpha=0.3)
    
    # 3. 年度連續性比較
    years = yearly_df['年份']
    continuity_ratios = yearly_df['連續性比例']
    colors = ['green' if ratio > 0.9 else 'orange' if ratio > 0.8 else 'red' for ratio in continuity_ratios]
    
    bars = ax3.bar(years, continuity_ratios, color=colors, alpha=0.7)
    ax3.set_title('年度連續性比較', fontproperties=font)
    ax3.set_xlabel('年份', fontproperties=font)
    ax3.set_ylabel('連續性比例', fontproperties=font)
    ax3.grid(True, alpha=0.3)
    
    # 添加數值標籤
    for bar, ratio in zip(bars, continuity_ratios):
        height = bar.get_height()
        ax3.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                f'{ratio:.3f}', ha='center', va='bottom', fontsize=8)
    
    # 4. 跳躍大小分析
    jump_sizes = df[df['period_diff'] > 1]['period_diff']
    if len(jump_sizes) > 0:
        ax4.hist(jump_sizes, bins=range(1, int(jump_sizes.max())+2), alpha=0.7, edgecolor='black')
        ax4.set_title('跳躍大小分布', fontproperties=font)
        ax4.set_xlabel('跳躍大小', fontproperties=font)
        ax4.set_ylabel('頻率', fontproperties=font)
        ax4.grid(True, alpha=0.3)
    else:
        ax4.text(0.5, 0.5, '無跳躍', ha='center', va='center', transform=ax4.transAxes, fontproperties=font)
        ax4.set_title('跳躍大小分布', fontproperties=font)
    
    plt.tight_layout()
    plt.show()

if __name__ == "__main__":
    df, trend_df, yearly_df = rolling_period_analysis()
    visualize_rolling_analysis(df, trend_df, yearly_df) 