import pandas as pd
import numpy as np

def analyze_missing_numbers():
    # 讀取CSV檔案
    df = pd.read_csv('data/lotto_history_2007_2025.csv')
    df.columns = ['filename', 'game_type', 'period', 'date', 'num1', 'num2', 'num3', 'num4', 'num5', 'num6']
    
    print('=== 未開出號碼統計分析 ===')
    print(f'總期數: {len(df)}')
    print()
    
    # 轉換期號為整數
    df['period'] = df['period'].astype(int)
    df['year'] = df['date'].str.split('/').str[0].astype(int)
    
    # 創建號碼列表
    number_columns = ['num1', 'num2', 'num3', 'num4', 'num5', 'num6']
    
    # 分析每一期的未開出號碼
    period_analysis = []
    
    for i, row in df.iterrows():
        # 獲取當期號碼
        current_numbers = sorted([row[col] for col in number_columns])
        
        # 找出未開出的號碼
        all_numbers = set(range(1, 50))  # 1-49
        drawn_numbers = set(current_numbers)
        missing_numbers = sorted(list(all_numbers - drawn_numbers))
        
        # 分析連續性
        missing_analysis = analyze_missing_continuity(missing_numbers)
        
        period_analysis.append({
            '期號': row['period'],
            '日期': row['date'],
            '年份': int(row['date'].split('/')[0]),
            '開出號碼': current_numbers,
            '未開出號碼': missing_numbers,
            '未開出數量': len(missing_numbers),
            '未開出連續組': missing_analysis['連續組'],
            '未開出間隔': missing_analysis['間隔'],
            '最大未開出間隔': missing_analysis['最大間隔'],
            '未開出連續組數': missing_analysis['連續組數'],
            '最大未開出連續長度': missing_analysis['最大連續長度']
        })
    
    period_df = pd.DataFrame(period_analysis)
    
    # 滾動窗口分析
    window_sizes = [10, 20, 50, 100]
    
    print('=== 滾動未開出號碼統計 ===')
    
    for window_size in window_sizes:
        print(f'\n--- {window_size}期滾動窗口分析 ---')
        
        rolling_stats = []
        
        for i in range(window_size - 1, len(period_df)):
            window_data = period_df.iloc[i-window_size+1:i+1]
            
            # 計算滾動統計
            avg_missing_count = window_data['未開出數量'].mean()
            avg_max_gap = window_data['最大未開出間隔'].mean()
            avg_consecutive_groups = window_data['未開出連續組數'].mean()
            
            # 統計該窗口內最常未開出的號碼
            all_missing_in_window = []
            for _, row in window_data.iterrows():
                all_missing_in_window.extend(row['未開出號碼'])
            
            missing_freq = pd.Series(all_missing_in_window).value_counts()
            most_missing_numbers = missing_freq.head(5).index.tolist()
            most_missing_freq = missing_freq.head(5).values.tolist()
            
            rolling_stats.append({
                '位置': i + 1,
                '開始期號': window_data.iloc[0]['期號'],
                '結束期號': window_data.iloc[-1]['期號'],
                '開始日期': window_data.iloc[0]['日期'],
                '結束日期': window_data.iloc[-1]['日期'],
                '平均未開出數量': avg_missing_count,
                '平均最大間隔': avg_max_gap,
                '平均連續組數': avg_consecutive_groups,
                '最常未開出號碼': most_missing_numbers,
                '最常未開出頻率': most_missing_freq
            })
        
        rolling_df = pd.DataFrame(rolling_stats)
        
        print(f'窗口大小: {window_size}期')
        print(f'滾動樣本數: {len(rolling_df)}')
        print(f'平均未開出數量: {rolling_df["平均未開出數量"].mean():.2f}')
        print(f'平均最大間隔: {rolling_df["平均最大間隔"].mean():.2f}')
        print(f'平均連續組數: {rolling_df["平均連續組數"].mean():.2f}')
        
        # 找出未開出號碼最多的窗口
        worst_windows = rolling_df.nlargest(3, '平均未開出數量')
        print(f'\n未開出號碼最多的3個{window_size}期窗口:')
        for _, row in worst_windows.iterrows():
            print(f'  位置{row["位置"]}: {row["開始期號"]}-{row["結束期號"]} '
                  f'({row["開始日期"]}-{row["結束日期"]}) '
                  f'平均未開出: {row["平均未開出數量"]:.2f}')
            print(f'    最常未開出號碼: {row["最常未開出號碼"]} (頻率: {row["最常未開出頻率"]})')
    
    # 詳細的每期分析
    print('\n=== 詳細每期未開出號碼分析 ===')
    print('顯示前20期和後20期的詳細分析:')
    
    # 前20期
    print('\n前20期分析:')
    for i in range(min(20, len(period_df))):
        row = period_df.iloc[i]
        print(f'第{i+1:3d}期: 期號{row["期號"]} ({row["日期"]})')
        print(f'  開出號碼: {row["開出號碼"]}')
        print(f'  未開出號碼: {row["未開出號碼"]}')
        print(f'  未開出數量: {row["未開出數量"]}, 最大間隔: {row["最大未開出間隔"]}')
        print(f'  未開出連續組: {row["未開出連續組"]}')
        print(f'  未開出間隔: {row["未開出間隔"]}')
        print()
    
    # 後20期
    print('\n後20期分析:')
    for i in range(max(0, len(period_df)-20), len(period_df)):
        row = period_df.iloc[i]
        print(f'第{i+1:3d}期: 期號{row["期號"]} ({row["日期"]})')
        print(f'  開出號碼: {row["開出號碼"]}')
        print(f'  未開出號碼: {row["未開出號碼"]}')
        print(f'  未開出數量: {row["未開出數量"]}, 最大間隔: {row["最大未開出間隔"]}')
        print(f'  未開出連續組: {row["未開出連續組"]}')
        print(f'  未開出間隔: {row["未開出間隔"]}')
        print()
    
    # 統計摘要
    print('\n=== 整體統計摘要 ===')
    print(f'總期數: {len(period_df)}')
    print(f'平均未開出數量: {period_df["未開出數量"].mean():.2f}')
    print(f'平均最大間隔: {period_df["最大未開出間隔"].mean():.2f}')
    print(f'平均連續組數: {period_df["未開出連續組數"].mean():.2f}')
    
    # 找出未開出號碼最多和最少的期數
    most_missing_period = period_df.loc[period_df['未開出數量'].idxmax()]
    least_missing_period = period_df.loc[period_df['未開出數量'].idxmin()]
    
    print(f'\n未開出號碼最多期數: 期號{most_missing_period["期號"]} ({most_missing_period["日期"]})')
    print(f'  開出號碼: {most_missing_period["開出號碼"]}')
    print(f'  未開出數量: {most_missing_period["未開出數量"]}')
    
    print(f'\n未開出號碼最少期數: 期號{least_missing_period["期號"]} ({least_missing_period["日期"]})')
    print(f'  開出號碼: {least_missing_period["開出號碼"]}')
    print(f'  未開出數量: {least_missing_period["未開出數量"]}')
    
    # 年度分析
    print('\n=== 年度未開出號碼分析 ===')
    yearly_stats = period_df.groupby('年份').agg({
        '未開出數量': ['mean', 'min', 'max'],
        '最大未開出間隔': 'mean',
        '未開出連續組數': 'mean'
    }).round(2)
    
    yearly_stats.columns = ['平均未開出數量', '最小未開出數量', '最大未開出數量', 
                           '平均最大間隔', '平均連續組數']
    
    print(yearly_stats)
    
    # 號碼頻率分析
    print('\n=== 號碼未開出頻率分析 ===')
    all_missing_numbers = []
    for _, row in period_df.iterrows():
        all_missing_numbers.extend(row['未開出號碼'])
    
    missing_freq = pd.Series(all_missing_numbers).value_counts().sort_index()
    
    print('各號碼未開出次數統計:')
    for number in range(1, 50):
        freq = missing_freq.get(number, 0)
        percentage = (freq / len(period_df)) * 100
        print(f'  號碼{number:2d}: {freq:4d}次 ({percentage:5.1f}%)')
    
    # 找出最常未開出和最常開出的號碼
    most_missing = missing_freq.nlargest(10)
    least_missing = missing_freq.nsmallest(10)
    
    print(f'\n最常未開出的10個號碼:')
    for number, freq in most_missing.items():
        percentage = (freq / len(period_df)) * 100
        print(f'  號碼{number:2d}: {freq:4d}次 ({percentage:5.1f}%)')
    
    print(f'\n最常開出的10個號碼:')
    for number, freq in least_missing.items():
        percentage = (freq / len(period_df)) * 100
        print(f'  號碼{number:2d}: {freq:4d}次 ({percentage:5.1f}%)')
    
    return period_df, missing_freq

def analyze_missing_continuity(missing_numbers):
    """分析未開出號碼的連續性"""
    if not missing_numbers:
        return {
            '連續組': [],
            '間隔': [],
            '最大間隔': 0,
            '連續組數': 0,
            '最大連續長度': 0
        }
    
    # 找出連續的號碼組
    consecutive_groups = []
    current_group = [missing_numbers[0]]
    
    for i in range(1, len(missing_numbers)):
        if missing_numbers[i] == missing_numbers[i-1] + 1:
            current_group.append(missing_numbers[i])
        else:
            if len(current_group) > 0:
                consecutive_groups.append(current_group)
            current_group = [missing_numbers[i]]
    
    if len(current_group) > 0:
        consecutive_groups.append(current_group)
    
    # 計算間隔
    gaps = []
    for i in range(1, len(missing_numbers)):
        gap = missing_numbers[i] - missing_numbers[i-1] - 1
        if gap > 0:
            gaps.append(gap)
    
    # 統計資訊
    max_consecutive = max(len(group) for group in consecutive_groups) if consecutive_groups else 0
    gap_count = len(gaps)
    max_gap = max(gaps) if gaps else 0
    
    return {
        '連續組': consecutive_groups,
        '間隔': gaps,
        '最大間隔': max_gap,
        '連續組數': len(consecutive_groups),
        '最大連續長度': max_consecutive
    }

def export_detailed_analysis(period_df, missing_freq):
    """匯出詳細分析結果到CSV"""
    # 準備匯出資料
    export_data = []
    
    for _, row in period_df.iterrows():
        export_data.append({
            '期號': row['期號'],
            '日期': row['日期'],
            '年份': row['年份'],
            '開出號碼': ','.join(map(str, row['開出號碼'])),
            '未開出號碼': ','.join(map(str, row['未開出號碼'])),
            '未開出數量': row['未開出數量'],
            '最大未開出間隔': row['最大未開出間隔'],
            '未開出連續組數': row['未開出連續組數'],
            '最大未開出連續長度': row['最大未開出連續長度'],
            '未開出連續組': ';'.join([','.join(map(str, group)) for group in row['未開出連續組']]),
            '未開出間隔': ','.join(map(str, row['未開出間隔'])) if row['未開出間隔'] else ''
        })
    
    export_df = pd.DataFrame(export_data)
    export_df.to_csv('missing_numbers_analysis.csv', index=False, encoding='utf-8-sig')
    print('\n詳細分析結果已匯出至 missing_numbers_analysis.csv')
    
    # 匯出號碼頻率統計
    freq_data = []
    for number in range(1, 50):
        freq = missing_freq.get(number, 0)
        percentage = (freq / len(period_df)) * 100
        freq_data.append({
            '號碼': number,
            '未開出次數': freq,
            '未開出百分比': percentage,
            '開出次數': len(period_df) - freq,
            '開出百分比': 100 - percentage
        })
    
    freq_df = pd.DataFrame(freq_data)
    freq_df.to_csv('number_frequency_analysis.csv', index=False, encoding='utf-8-sig')
    print('號碼頻率統計已匯出至 number_frequency_analysis.csv')

if __name__ == "__main__":
    period_df, missing_freq = analyze_missing_numbers()
    export_detailed_analysis(period_df, missing_freq) 