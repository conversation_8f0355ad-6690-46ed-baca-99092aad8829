import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.font_manager import FontProperties

def analyze_number_continuity():
    # 讀取CSV檔案
    df = pd.read_csv('data/lotto_history_2007_2025.csv')
    df.columns = ['filename', 'game_type', 'period', 'date', 'num1', 'num2', 'num3', 'num4', 'num5', 'num6']
    
    print('=== 滾動號碼連續性分析 ===')
    print(f'總期數: {len(df)}')
    print()
    
    # 轉換期號為整數
    df['period'] = df['period'].astype(int)
    df['year'] = df['date'].str.split('/').str[0].astype(int)
    
    # 創建號碼列表
    number_columns = ['num1', 'num2', 'num3', 'num4', 'num5', 'num6']
    
    # 分析每一期的號碼連續性
    period_analysis = []
    
    for i, row in df.iterrows():
        # 獲取當期號碼
        current_numbers = sorted([row[col] for col in number_columns])
        
        # 分析連續性
        continuity_info = analyze_single_period(current_numbers, row['period'], row['date'])
        period_analysis.append(continuity_info)
    
    period_df = pd.DataFrame(period_analysis)
    
    # 滾動窗口分析
    window_sizes = [10, 20, 50, 100]
    
    print('=== 滾動號碼連續性統計 ===')
    
    for window_size in window_sizes:
        print(f'\n--- {window_size}期滾動窗口分析 ---')
        
        rolling_stats = []
        
        for i in range(window_size - 1, len(period_df)):
            window_data = period_df.iloc[i-window_size+1:i+1]
            
            # 計算滾動統計
            avg_continuous_count = window_data['連續號碼數'].mean()
            avg_gap_count = window_data['間隔數'].mean()
            avg_max_gap = window_data['最大間隔'].mean()
            avg_consecutive_groups = window_data['連續組數'].mean()
            
            rolling_stats.append({
                '位置': i + 1,
                '開始期號': window_data.iloc[0]['期號'],
                '結束期號': window_data.iloc[-1]['期號'],
                '開始日期': window_data.iloc[0]['日期'],
                '結束日期': window_data.iloc[-1]['日期'],
                '平均連續號碼數': avg_continuous_count,
                '平均間隔數': avg_gap_count,
                '平均最大間隔': avg_max_gap,
                '平均連續組數': avg_consecutive_groups
            })
        
        rolling_df = pd.DataFrame(rolling_stats)
        
        print(f'窗口大小: {window_size}期')
        print(f'滾動樣本數: {len(rolling_df)}')
        print(f'平均連續號碼數: {rolling_df["平均連續號碼數"].mean():.2f}')
        print(f'平均間隔數: {rolling_df["平均間隔數"].mean():.2f}')
        print(f'平均最大間隔: {rolling_df["平均最大間隔"].mean():.2f}')
        print(f'平均連續組數: {rolling_df["平均連續組數"].mean():.2f}')
        
        # 找出連續性最差的窗口
        worst_windows = rolling_df.nsmallest(3, '平均連續號碼數')
        print(f'\n連續性最差的3個{window_size}期窗口:')
        for _, row in worst_windows.iterrows():
            print(f'  位置{row["位置"]}: {row["開始期號"]}-{row["結束期號"]} '
                  f'({row["開始日期"]}-{row["結束日期"]}) '
                  f'平均連續號碼: {row["平均連續號碼數"]:.2f}')
    
    # 詳細的每期分析
    print('\n=== 詳細每期號碼連續性分析 ===')
    print('顯示前20期和後20期的詳細分析:')
    
    # 前20期
    print('\n前20期分析:')
    for i in range(min(20, len(period_df))):
        row = period_df.iloc[i]
        print(f'第{i+1:3d}期: 期號{row["期號"]} ({row["日期"]})')
        print(f'  號碼: {row["號碼"]}')
        print(f'  連續號碼數: {row["連續號碼數"]}, 間隔數: {row["間隔數"]}, 最大間隔: {row["最大間隔"]}')
        print(f'  連續組: {row["連續組"]}')
        print(f'  間隔: {row["間隔"]}')
        print()
    
    # 後20期
    print('\n後20期分析:')
    for i in range(max(0, len(period_df)-20), len(period_df)):
        row = period_df.iloc[i]
        print(f'第{i+1:3d}期: 期號{row["期號"]} ({row["日期"]})')
        print(f'  號碼: {row["號碼"]}')
        print(f'  連續號碼數: {row["連續號碼數"]}, 間隔數: {row["間隔數"]}, 最大間隔: {row["最大間隔"]}')
        print(f'  連續組: {row["連續組"]}')
        print(f'  間隔: {row["間隔"]}')
        print()
    
    # 統計摘要
    print('\n=== 整體統計摘要 ===')
    print(f'總期數: {len(period_df)}')
    print(f'平均連續號碼數: {period_df["連續號碼數"].mean():.2f}')
    print(f'平均間隔數: {period_df["間隔數"].mean():.2f}')
    print(f'平均最大間隔: {period_df["最大間隔"].mean():.2f}')
    print(f'平均連續組數: {period_df["連續組數"].mean():.2f}')
    
    # 找出連續性最好和最差的期數
    best_period = period_df.loc[period_df['連續號碼數'].idxmax()]
    worst_period = period_df.loc[period_df['連續號碼數'].idxmin()]
    
    print(f'\n連續性最佳期數: 期號{best_period["期號"]} ({best_period["日期"]})')
    print(f'  號碼: {best_period["號碼"]}')
    print(f'  連續號碼數: {best_period["連續號碼數"]}')
    
    print(f'\n連續性最差期數: 期號{worst_period["期號"]} ({worst_period["日期"]})')
    print(f'  號碼: {worst_period["號碼"]}')
    print(f'  連續號碼數: {worst_period["連續號碼數"]}')
    
    # 年度分析
    print('\n=== 年度號碼連續性分析 ===')
    yearly_stats = period_df.groupby('年份').agg({
        '連續號碼數': ['mean', 'min', 'max'],
        '間隔數': 'mean',
        '最大間隔': 'mean',
        '連續組數': 'mean'
    }).round(2)
    
    yearly_stats.columns = ['平均連續號碼數', '最小連續號碼數', '最大連續號碼數', 
                           '平均間隔數', '平均最大間隔', '平均連續組數']
    
    print(yearly_stats)
    
    return period_df

def analyze_single_period(numbers, period, date):
    """分析單一期的號碼連續性"""
    # 排序號碼
    sorted_numbers = sorted(numbers)
    
    # 找出連續的號碼組
    consecutive_groups = []
    current_group = [sorted_numbers[0]]
    
    for i in range(1, len(sorted_numbers)):
        if sorted_numbers[i] == sorted_numbers[i-1] + 1:
            current_group.append(sorted_numbers[i])
        else:
            if len(current_group) > 0:
                consecutive_groups.append(current_group)
            current_group = [sorted_numbers[i]]
    
    if len(current_group) > 0:
        consecutive_groups.append(current_group)
    
    # 計算間隔
    gaps = []
    for i in range(1, len(sorted_numbers)):
        gap = sorted_numbers[i] - sorted_numbers[i-1] - 1
        if gap > 0:
            gaps.append(gap)
    
    # 統計資訊
    max_consecutive = max(len(group) for group in consecutive_groups) if consecutive_groups else 0
    total_consecutive = sum(len(group) for group in consecutive_groups if len(group) > 1)
    gap_count = len(gaps)
    max_gap = max(gaps) if gaps else 0
    
    return {
        '期號': period,
        '日期': date,
        '年份': int(date.split('/')[0]),
        '號碼': sorted_numbers,
        '連續號碼數': total_consecutive,
        '間隔數': gap_count,
        '最大間隔': max_gap,
        '連續組數': len(consecutive_groups),
        '連續組': consecutive_groups,
        '間隔': gaps,
        '最大連續長度': max_consecutive
    }

def visualize_number_continuity(period_df):
    """視覺化號碼連續性分析"""
    try:
        font = FontProperties(fname=r"c:\windows\fonts\msjh.ttc", size=12)
    except FileNotFoundError:
        try:
            font = FontProperties(fname="/System/Library/Fonts/PingFang.ttc", size=12)
        except FileNotFoundError:
            font = FontProperties(size=12)
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('號碼連續性分析', fontsize=16, fontproperties=font)
    
    # 1. 連續號碼數分布
    ax1.hist(period_df['連續號碼數'], bins=range(0, int(period_df['連續號碼數'].max())+2), 
             alpha=0.7, edgecolor='black')
    ax1.set_title('連續號碼數分布', fontproperties=font)
    ax1.set_xlabel('連續號碼數', fontproperties=font)
    ax1.set_ylabel('頻率', fontproperties=font)
    ax1.grid(True, alpha=0.3)
    
    # 2. 連續號碼數時間序列
    ax2.plot(range(len(period_df)), period_df['連續號碼數'], 'b-', alpha=0.7)
    ax2.set_title('連續號碼數時間序列', fontproperties=font)
    ax2.set_xlabel('期數順序', fontproperties=font)
    ax2.set_ylabel('連續號碼數', fontproperties=font)
    ax2.grid(True, alpha=0.3)
    
    # 3. 年度平均連續號碼數
    yearly_avg = period_df.groupby('年份')['連續號碼數'].mean()
    ax3.bar(yearly_avg.index, yearly_avg.values, alpha=0.7)
    ax3.set_title('年度平均連續號碼數', fontproperties=font)
    ax3.set_xlabel('年份', fontproperties=font)
    ax3.set_ylabel('平均連續號碼數', fontproperties=font)
    ax3.grid(True, alpha=0.3)
    
    # 4. 間隔數分布
    ax4.hist(period_df['間隔數'], bins=range(0, int(period_df['間隔數'].max())+2), 
             alpha=0.7, edgecolor='black')
    ax4.set_title('間隔數分布', fontproperties=font)
    ax4.set_xlabel('間隔數', fontproperties=font)
    ax4.set_ylabel('頻率', fontproperties=font)
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()

def export_detailed_analysis(period_df):
    """匯出詳細分析結果到CSV"""
    # 準備匯出資料
    export_data = []
    
    for _, row in period_df.iterrows():
        export_data.append({
            '期號': row['期號'],
            '日期': row['日期'],
            '年份': row['年份'],
            '號碼': ','.join(map(str, row['號碼'])),
            '連續號碼數': row['連續號碼數'],
            '間隔數': row['間隔數'],
            '最大間隔': row['最大間隔'],
            '連續組數': row['連續組數'],
            '最大連續長度': row['最大連續長度'],
            '連續組': ';'.join([','.join(map(str, group)) for group in row['連續組']]),
            '間隔': ','.join(map(str, row['間隔'])) if row['間隔'] else ''
        })
    
    export_df = pd.DataFrame(export_data)
    export_df.to_csv('number_continuity_analysis.csv', index=False, encoding='utf-8-sig')
    print('\n詳細分析結果已匯出至 number_continuity_analysis.csv')

if __name__ == "__main__":
    period_df = analyze_number_continuity()
    visualize_number_continuity(period_df)
    export_detailed_analysis(period_df) 