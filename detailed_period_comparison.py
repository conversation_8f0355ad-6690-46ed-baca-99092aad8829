import pandas as pd
import numpy as np

def detailed_period_comparison():
    # 讀取CSV檔案
    df = pd.read_csv('data/lotto_history_2007_2025.csv', header=None, 
                     names=['filename', 'game_type', 'period', 'date', 'num1', 'num2', 'num3', 'num4', 'num5', 'num6'])
    
    print('=== 詳細期數對比分析 ===')
    print(f'總期數: {len(df)}')
    print()
    
    # 轉換期號為整數
    df['period'] = df['period'].astype(int)
    df['year'] = df['date'].str.split('/').str[0].astype(int)
    
    # 創建詳細的期數對比表
    comparison_data = []
    
    for i in range(len(df)):
        row = df.iloc[i]
        period = row['period']
        date = row['date']
        year = row['year']
        
        # 計算期號的組成部分
        year_prefix = period // 1000000  # 前兩位數字 (104, 105, 106...)
        sequence_number = period % 1000000  # 後六位數字
        
        comparison_data.append({
            '行號': i + 1,
            '期號': period,
            '日期': date,
            '年份': year,
            '年份前綴': year_prefix,
            '序號': sequence_number,
            '預期年份前綴': 104 + (year - 2015),  # 2015年應該是104開頭
            '前綴是否正確': year_prefix == (104 + (year - 2015))
        })
    
    comparison_df = pd.DataFrame(comparison_data)
    
    # 檢查期號前綴是否正確
    print('=== 期號前綴檢查 ===')
    incorrect_prefixes = comparison_df[~comparison_df['前綴是否正確']]
    if len(incorrect_prefixes) > 0:
        print(f'發現 {len(incorrect_prefixes)} 個期號前綴不正確:')
        for _, row in incorrect_prefixes.iterrows():
            print(f'  第 {row["行號"]} 行: 期號 {row["期號"]}, 年份 {row["年份"]}, 前綴 {row["年份前綴"]}, 預期 {row["預期年份前綴"]}')
    else:
        print('所有期號前綴都正確')
    print()
    
    # 檢查序號連續性
    print('=== 序號連續性檢查 ===')
    sequence_gaps = []
    
    for i in range(1, len(comparison_df)):
        prev_seq = comparison_df.iloc[i-1]['序號']
        curr_seq = comparison_df.iloc[i]['序號']
        prev_year = comparison_df.iloc[i-1]['年份']
        curr_year = comparison_df.iloc[i]['年份']
        
        if prev_year == curr_year:  # 同一年內
            if curr_seq != prev_seq + 1:
                sequence_gaps.append({
                    '行號': i + 1,
                    '前一期號': comparison_df.iloc[i-1]['期號'],
                    '當前期號': comparison_df.iloc[i]['期號'],
                    '前序號': prev_seq,
                    '當前序號': curr_seq,
                    '跳躍大小': curr_seq - prev_seq - 1,
                    '年份': curr_year
                })
        else:  # 跨年
            if curr_seq != 1:  # 新年第一期的序號應該是1
                sequence_gaps.append({
                    '行號': i + 1,
                    '前一期號': comparison_df.iloc[i-1]['期號'],
                    '當前期號': comparison_df.iloc[i]['期號'],
                    '前序號': prev_seq,
                    '當前序號': curr_seq,
                    '跳躍大小': '跨年',
                    '年份': curr_year
                })
    
    if sequence_gaps:
        print(f'發現 {len(sequence_gaps)} 個序號不連續:')
        for gap in sequence_gaps:
            print(f'  第 {gap["行號"]} 行: {gap["前一期號"]} -> {gap["當前期號"]}')
            print(f'    序號: {gap["前序號"]} -> {gap["當前序號"]}')
            print(f'    跳躍: {gap["跳躍大小"]}')
            print(f'    年份: {gap["年份"]}')
            print()
    else:
        print('所有序號都連續')
    print()
    
    # 年度期數統計
    print('=== 年度期數詳細統計 ===')
    yearly_stats = df.groupby('year').agg({
        'period': ['count', 'min', 'max'],
        'date': ['min', 'max']
    }).round(0)
    
    yearly_stats.columns = ['期數', '最小期號', '最大期號', '開始日期', '結束日期']
    
    for year in sorted(df['year'].unique()):
        year_data = df[df['year'] == year]
        year_periods = year_data['period'].values
        
        print(f'\n{year}年:')
        print(f'  期數: {len(year_data)}')
        print(f'  期號範圍: {year_periods.min()} - {year_periods.max()}')
        print(f'  日期範圍: {year_data["date"].min()} - {year_data["date"].max()}')
        
        # 檢查該年度的期號模式
        expected_prefix = 104 + (year - 2015)
        actual_prefixes = set([p // 1000000 for p in year_periods])
        
        if len(actual_prefixes) == 1 and list(actual_prefixes)[0] == expected_prefix:
            print(f'  期號前綴: {list(actual_prefixes)[0]} (正確)')
        else:
            print(f'  期號前綴: {actual_prefixes} (異常，預期: {expected_prefix})')
        
        # 檢查序號是否從1開始連續
        sequence_numbers = [p % 1000000 for p in year_periods]
        if sequence_numbers[0] == 1 and sequence_numbers == list(range(1, len(sequence_numbers) + 1)):
            print(f'  序號: 連續 (1-{len(sequence_numbers)})')
        else:
            print(f'  序號: 不連續 {sequence_numbers}')
    
    # 期號模式分析
    print('\n=== 期號模式分析 ===')
    print('期號格式: YYXXXXXX')
    print('  YY: 年份前綴 (104=2015, 105=2016, ...)')
    print('  XXXXXX: 6位序號 (000001, 000002, ...)')
    print()
    
    # 檢查是否有異常的期號格式
    print('=== 異常期號檢查 ===')
    abnormal_periods = []
    
    for i, row in comparison_df.iterrows():
        period = row['期號']
        year = row['年份']
        expected_prefix = 104 + (year - 2015)
        actual_prefix = row['年份前綴']
        sequence = row['序號']
        
        # 檢查各種異常情況
        issues = []
        
        if actual_prefix != expected_prefix:
            issues.append(f'前綴錯誤 (實際:{actual_prefix}, 預期:{expected_prefix})')
        
        if sequence < 1 or sequence > 999999:
            issues.append(f'序號超出範圍 ({sequence})')
        
        if len(str(period)) != 9:
            issues.append(f'期號長度錯誤 ({len(str(period))}位)')
        
        if issues:
            abnormal_periods.append({
                '行號': i + 1,
                '期號': period,
                '日期': row['日期'],
                '年份': year,
                '問題': '; '.join(issues)
            })
    
    if abnormal_periods:
        print(f'發現 {len(abnormal_periods)} 個異常期號:')
        for abnormal in abnormal_periods:
            print(f'  第 {abnormal["行號"]} 行: {abnormal["期號"]} ({abnormal["日期"]})')
            print(f'    問題: {abnormal["問題"]}')
            print()
    else:
        print('沒有發現異常期號')
    
    # 期號跳躍分析
    print('=== 期號跳躍詳細分析 ===')
    jumps = []
    
    for i in range(1, len(df)):
        prev_period = df.iloc[i-1]['period']
        curr_period = df.iloc[i]['period']
        prev_year = df.iloc[i-1]['year']
        curr_year = df.iloc[i]['year']
        
        if curr_period != prev_period + 1:
            jump_size = curr_period - prev_period
            jumps.append({
                '行號': i + 1,
                '前一期': prev_period,
                '當前期': curr_period,
                '跳躍大小': jump_size,
                '前一年': prev_year,
                '當前年': curr_year,
                '是否跨年': prev_year != curr_year
            })
    
    if jumps:
        print(f'發現 {len(jumps)} 個期號跳躍:')
        for jump in jumps:
            print(f'  第 {jump["行號"]} 行: {jump["前一期"]} -> {jump["當前期"]}')
            print(f'    跳躍大小: {jump["跳躍大小"]}')
            print(f'    年份: {jump["前一年"]} -> {jump["當前年"]}')
            print(f'    跨年: {"是" if jump["是否跨年"] else "否"}')
            print()
    else:
        print('沒有發現期號跳躍')
    
    # 總結
    print('=== 分析總結 ===')
    print(f'總期數: {len(df)}')
    print(f'期號範圍: {df["period"].min()} - {df["period"].max()}')
    print(f'年份範圍: {df["year"].min()} - {df["year"].max()}')
    print(f'期號跳躍次數: {len(jumps)}')
    print(f'異常期號數量: {len(abnormal_periods)}')
    
    # 檢查資料完整性
    total_expected_periods = 0
    for year in range(2015, 2026):
        year_data = df[df['year'] == year]
        if len(year_data) > 0:
            total_expected_periods += len(year_data)
    
    print(f'資料完整性: {len(df)}/{total_expected_periods} ({len(df)/total_expected_periods*100:.1f}%)')
    
    return comparison_df, jumps, abnormal_periods

if __name__ == "__main__":
    comparison_df, jumps, abnormal_periods = detailed_period_comparison() 